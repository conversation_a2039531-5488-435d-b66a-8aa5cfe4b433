/*
** ###################################################################
**     Processors:          MIMXRT1062CVJ5A
**                          MIMXRT1062CVL5A
**                          MIMXRT1062DVJ6A
**                          MIMXRT1062DVL6A
**
**     Compiler:            IAR ANSI C/C++ Compiler for ARM
**     Reference manual:    IMXRT1060RM Rev. B, 07/2018
**     Version:             rev. 0.1, 2017-01-10
**     Build:               b180801
**
**     Abstract:
**         Linker file for the IAR ANSI C/C++ Compiler for ARM
**
**     Copyright 2016 Freescale Semiconductor, Inc.
**     Copyright 2016-2018 NXP
**
**     SPDX-License-Identifier: BSD-3-Clause
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
**
** ###################################################################
*/

define symbol m_interrupts_start       = 0x70002000;
define symbol m_interrupts_end         = 0x700023FF;

define symbol m_text_start             = 0x70002400;
define symbol m_text_end               = 0x703FFFFF;


/* SDRAM IVT_TABLE */
define symbol m_ram_interrupts_start   = 0x80000000;
define symbol m_ram_interrupts_end     = 0x800003FF;

/* SDRAM */
define symbol m_ram_text_start         = 0x80000400;
define symbol m_ram_text_end           = 0x81DFFFFF;

/* DTCM */
define symbol m_data_start             = 0x20000000;
define symbol m_data_end               = 0x2006FFFF;

/* OCRAM */
define symbol m_data2_start            = 0x20200000;
define symbol m_data2_end              = 0x2027FFFF;

/* ITCM */
define symbol m_data3_start             = 0x00000004;
define symbol m_data3_end               = 0x0000FFFF;

/* SDRAM ncache */
define symbol m_ncache_start           = 0x81E00000;
define symbol m_ncache_end             = 0x81FFFFFF;

define exported symbol m_boot_hdr_conf_start = 0x70000000;
define symbol m_boot_hdr_ivt_start           = 0x70001000;
define symbol m_boot_hdr_boot_data_start     = 0x70001020;
define symbol m_boot_hdr_dcd_data_start      = 0x70001030;

/* Sizes */
if (isdefinedsymbol(__stack_size__)) {
  define symbol __size_cstack__        = __stack_size__;
} else {
  define symbol __size_cstack__        = 0x1000;
}

if (isdefinedsymbol(__heap_size__)) {
  define symbol __size_heap__          = __heap_size__;
} else {
  define symbol __size_heap__          = 0x400;
}

define exported symbol __VECTOR_TABLE  = m_interrupts_start;
define exported symbol __VECTOR_RAM    = m_ram_interrupts_start;
define exported symbol __RAM_VECTOR_TABLE_SIZE = m_interrupts_end-m_interrupts_start+1;
define exported symbol __CSTACK_ADDRESS  = m_data_end+1;

define memory mem with size = 4G;
define region TEXT_region = mem:[from m_interrupts_start to m_interrupts_end]
                          | mem:[from m_text_start to m_text_end];

define region RAM_TEXT_region = mem:[from m_ram_interrupts_start to m_ram_interrupts_end]
                                | mem:[from m_ram_text_start to m_ram_text_end];  

define region DATA_region = mem:[from m_data_start to m_data_end-__size_cstack__];
define region DATA2_region = mem:[from m_data2_start to m_data2_end];
define region DATA3_region = mem:[from m_data3_start to m_data3_end];
define region SDRAM_NCACHE_region = mem:[from m_ncache_start to m_ncache_end];
define region CSTACK_region = mem:[from m_data_end-__size_cstack__+1 to m_data_end];

define block CSTACK    with alignment = 8, size = __size_cstack__   { };
define block HEAP      with alignment = 8, size = __size_heap__     { };
define block RW        { first readwrite, section m_usb_dma_init_data };/*define block RW        { readwrite };*/
define block ZI        with alignment = 32 { first zi, section m_usb_dma_noninit_data };/*{ zi };*/
define block DTCM_VAR  with alignment = 8  { section NonCacheable , section NonCacheable.init };
define block ITCM_VAR  with alignment = 8  { section ITCM_NonCacheable , section ITCM_NonCacheable.init };
define block OCRAM_VAR with alignment = 8  { section OCRAM_CACHE , section OCRAM_CACHE.init };
define block SDRAM_VAR with alignment = 8  { section SDRAM_CACHE , section SDRAM_CACHE.init };
define block SDRAM_NCACHE_VAR with alignment = 8  { section SDRAM_NonCacheable , section SDRAM_NonCacheable.init };

initialize by copy{ readwrite, section .textrw };
initialize by copy{ section NonCacheable.init};
initialize by copy{ section ITCM_NonCacheable.init};
initialize by copy{ section OCRAM_CACHE.init};
initialize by copy{ section SDRAM_CACHE.init};
initialize by copy{ section SDRAM_NonCacheable.init};
initialize by copy{ readonly}
except{ 
		readonly section .intvec,
		readonly object system_MIMXRT1064.o,
		readonly object startup_MIMXRT1064.o,
		
		section .boot_hdr.conf, 
		section .boot_hdr.ivt, 
		section .boot_hdr.boot_data, 
		section .boot_hdr.dcd_data 
	};


do not initialize  { section .noinit };

place at address mem: m_interrupts_start    { readonly section .intvec };
/* RAM vector table*/
place at address mem: m_ram_interrupts_start    {  section .intvec_RAM };

place at address mem:m_boot_hdr_conf_start { section .boot_hdr.conf };
place at address mem:m_boot_hdr_ivt_start { section .boot_hdr.ivt };
place at address mem:m_boot_hdr_boot_data_start { readonly section .boot_hdr.boot_data };
place at address mem:m_boot_hdr_dcd_data_start { readonly section .boot_hdr.dcd_data };

keep{ section .boot_hdr.conf, section .boot_hdr.ivt, section .boot_hdr.boot_data, section .boot_hdr.dcd_data };

/* code addr*/
place in TEXT_region                        { readonly };

/* RW data*/
place in RAM_TEXT_region                	{ block RW};

place in DATA_region                        { block ZI };
place in DATA_region                        { section .data };
place in DATA_region                        { section .textrw };
place in DATA_region                        { last block HEAP };
place in CSTACK_region                      { block CSTACK };

place in DATA3_region                       { block ITCM_VAR };
place in DATA_region                        { block DTCM_VAR };
place in DATA2_region                       { block OCRAM_VAR};
place in RAM_TEXT_region                	{ block SDRAM_VAR};
place in SDRAM_NCACHE_region          		{ block SDRAM_NCACHE_VAR};



