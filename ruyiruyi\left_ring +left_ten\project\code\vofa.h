/********************************************************************************
* @File name: vofa+.h
* @Author: Flexible
* @Version: 3.0
* @Date: 2024-01-19
* @Description: 上位机VOFA+的pid调参通讯协议
********************************************************************************/
#ifndef VOFA_H
#define VOFA_H
#include "zf_common_headfile.h"

//联合体：同一个内存空间中存储不同的数据类型
typedef union     
{
	float fdata;
	unsigned long ldata;
}FloatLongType;


//参数外部声明，方便外部调用

extern float data_2;
extern float data_3;
extern float data_4;
extern float data_5;
extern float data_6;
extern float data_7;


void VOFA_JustFloat(void);
void float_turn_u8(float f,uint8_t * c);
//void uart_putchar(UARTN_enum uartn, uint8 dat);

#endif /*VOFA_H*/
